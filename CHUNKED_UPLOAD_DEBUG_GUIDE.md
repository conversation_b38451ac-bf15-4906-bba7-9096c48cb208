# Chunked Upload Debug Guide - iOS Issue Investigation

## Overview
Comprehensive logging has been added to `packages/bAudit/src/helpers/useChunkedUpload.tsx` to debug the iOS-specific issue where uploads get stuck on "uploading chunk 1 of 3".

## Logging Categories Added

### 🚀 **Function Entry Logging**
- All input parameters (file path, destination, tokens, etc.)
- Platform detection (iOS vs Android)
- User and company information

### 📊 **File Analysis Logging**
- File size calculation
- Chunk size determination (4MB)
- Total chunks expected
- Initial offset and session ID values

### 🔄 **Loop Iteration Logging**
- Each while loop iteration with current position
- Background job status checks (critical for iOS)
- Chunk boundaries (start, end, size)
- Progress percentage

### 📤📥 **Network Request Logging**
- **START Phase**: First chunk upload initiation
- **APPEND Phase**: Middle chunks upload
- **FINISH Phase**: Final chunk and file completion
- Request details (URL, headers, body size)
- Response status, headers, and data
- JSON parsing success/failure

### 📍 **State Management Logging**
- Offset calculations and updates
- Session ID management
- Redux dispatch calls
- Chunk counter updates

### ❌ **Error Handling Logging**
- Network request failures
- JSON parsing errors
- Background job termination
- iOS-specific error conditions

## Key Log Patterns to Watch For

### **Normal Flow Pattern:**
```
🚀 CHUNKED UPLOAD STARTED
📊 File Size: [size] bytes
📈 Total Chunks Expected: [count]
🔄 Starting upload loop...

🔄 === CHUNK LOOP ITERATION ===
📈 Processing chunk 1 of [total]
🚀 === START PHASE (First Chunk) ===
📤 Making START request...
📥 START response received: {status: 200}
✅ START call successful!
🎫 Session ID received: [session_id]

🔄 === CHUNK LOOP ITERATION ===
📈 Processing chunk 2 of [total]
📎 === APPEND PHASE (Middle Chunk) ===
📤 Making APPEND request...
📥 APPEND response received: {status: 200}
✅ APPEND chunk uploaded successfully!

🔄 === CHUNK LOOP ITERATION ===
📈 Processing chunk 3 of [total]
🏁 === FINISH PHASE (Final Chunk) ===
📤 Making FINISH request...
📥 FINISH response received: {status: 200}
✅ FINISH call successful!
🎉 Upload completed successfully!
```

### **Potential Failure Points to Investigate:**

#### 1. **Background Job Termination (iOS)**
Look for:
```
🔧 Background job running: false
❌ iOS: Background job stopped, aborting upload
```

#### 2. **Network Request Failures**
Look for:
```
❌ START request failed: [error]
❌ APPEND request failed: [error]
❌ FINISH request failed: [error]
```

#### 3. **Response Parsing Issues**
Look for:
```
❌ Failed to parse [PHASE] response JSON: [error]
```

#### 4. **API Response Errors**
Look for:
```
❌ [PHASE] call failed with status [status]
```

#### 5. **Offset/Session Issues**
Look for:
```
📍 Offset updated: [old] → [new]
🎫 Session ID: [id]
```

## Debugging Steps

### **Step 1: Check Function Entry**
Verify all parameters are correct:
- File path exists and is accessible
- Cloud storage token is present
- Destination path is valid

### **Step 2: Monitor Loop Progression**
- Does the loop start?
- Does it progress beyond chunk 1?
- Are chunk boundaries calculated correctly?

### **Step 3: Analyze Network Requests**
- Are START requests being made?
- What's the response status?
- Is the session ID being received?

### **Step 4: Check iOS Background Handling**
- Is the background job running throughout?
- Does it terminate unexpectedly?

### **Step 5: Verify State Updates**
- Are offsets being calculated correctly?
- Is the session ID persisting between chunks?

## Common iOS Issues to Look For

### **Background App Refresh Disabled**
```
🔧 Background job running: false
```
*Solution: Enable Background App Refresh for the app*

### **Network Connectivity Issues**
```
❌ Network request failed
```
*Solution: Check network stability and retry logic*

### **Memory Pressure**
```
❌ Failed to read chunk data
```
*Solution: iOS may be terminating due to memory pressure*

### **Token Expiration**
```
❌ [PHASE] call failed with status 401
```
*Solution: Refresh authentication token*

## Log Analysis Commands

### **Filter for Errors:**
```bash
# In Reactotron or console
grep "❌" logs.txt
```

### **Track Chunk Progression:**
```bash
grep "📈 Processing chunk" logs.txt
```

### **Monitor Background Job:**
```bash
grep "🔧 Background job" logs.txt
```

### **Check Network Requests:**
```bash
grep "📤 Making.*request" logs.txt
```

## Expected Behavior After Logging

With these logs, you should be able to identify:
1. **Exact failure point** - Which phase fails (START/APPEND/FINISH)
2. **iOS-specific issues** - Background job termination
3. **Network problems** - Request/response failures
4. **State management issues** - Offset/session ID problems
5. **API errors** - Dropbox API response issues

## Next Steps

1. **Run the upload** with a file >4MB on iOS
2. **Capture all logs** from Reactotron/console
3. **Analyze the log pattern** against the expected flow
4. **Identify the exact failure point** using the emoji markers
5. **Apply targeted fixes** based on the specific issue found

The comprehensive logging will reveal exactly where and why the upload gets stuck on chunk 1.
