import { Alert, Platform } from "react-native";
import { <PERSON>uff<PERSON> } from "buffer";
import { useDispatch } from "react-redux";
import { TRACKING_EVENTS, eventsService } from "bcomponents";
import { setOffset, setSessionId } from "../../../core/reducers";

import RNFS from "react-native-fs";
import RNFetchBlob from "react-native-blob-util";
import BackgroundJob from "react-native-background-actions";
import fetchWithRetry from "./fetchWithDelay";

const useChunkedUpload = () => {
  const dispatch = useDispatch();

  const calculateTotalChunks = async (filePath) => {
    const fileUri = `${filePath}`;
    const fileInfo = await RNFetchBlob.fs.stat(fileUri);
    const fileSize = fileInfo.size;
    const chunkSize = 4 * 1024 * 1024;
    const result = Math.ceil(fileSize / chunkSize); // Use Math.ceil to round up
    return result;
  };

  const chunkedUpload = async (
    filePath: string,
    destinationPath: string,
    cloudStorageFolder: string,
    cloudStorageToken: string,
    inspectionId: string,
    savedOffset = 0,
    savedSessionId: string | null,
    setCurrentChunk: (chunk: number) => void,
    setIsUploading: (isUploading: boolean) => void,
    handleUpdateNotification: () => void,
    userName: string,
    companyPrefix: string
  ) => {
    const chunkSize = 4 * 1024 * 1024;
    const fileUri = `${filePath}`;
    const file = await RNFetchBlob.fs.stat(fileUri);

    const fileSize = file.size;
    let start = savedOffset ? savedOffset : 0;
    let sessionId = savedSessionId ? savedSessionId : "";
    let chunkIndex = savedOffset ? savedOffset / chunkSize : 0;
    let offset = savedOffset ? savedOffset : 0;

    while (start < fileSize) {
      // Check if background job is still running (iOS specific check)
      if (Platform.OS === "ios" && !BackgroundJob.isRunning()) {
        console.log("Background job stopped, aborting upload");
        setCurrentChunk(0);
        return -1;
      }

      setIsUploading(true);
      chunkIndex++;
      setCurrentChunk(chunkIndex);

      const end = Math.min(start + chunkSize, fileSize);
      const chunk = await RNFS.read(fileUri, chunkSize, start, "base64");

      let url = "https://content.dropboxapi.com/2/files/upload_session/";
      let headers = {
        Authorization: `Bearer ${cloudStorageToken}`,
        "Content-Type": "application/octet-stream",
        "Dropbox-API-Arg": JSON.stringify({
          close: false,
        }),
      };
      // Convert base64 string back to binary
      const binaryData = Buffer.from(chunk, "base64");
      let body = binaryData;

      if (start === 0) {
        url += "start";
        let response;
        try {
          response = await fetchWithRetry(url, {
            method: "POST",
            headers: headers,
            body: body,
          });
        } catch (error) {
          console.error("Failed to upload the chunk: ", error);
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to upload the chunk, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(
            TRACKING_EVENTS.CHUNK_UPLOAD_START_FAILED,
            {
              error: error,
              offset: offset,
              userName,
              companyPrefix,
            }
          );

          return -1;
        }

        let data = await response.json();

        if (response === -1) {
          console.log("Error in start call: ", data);
          await BackgroundJob.stop();
          return -1;
        }

        if (response?.status !== 200) {
          console.log("Error in start call: ", data);
          await BackgroundJob.stop();
          return -1;
        }

        handleUpdateNotification();
        offset += chunkSize;
        sessionId = data.session_id;
        console.log("Start call successful: ", data);
        dispatch(setSessionId({ id: inspectionId, sessionId: sessionId }));
      } else if (end < fileSize) {
        url += "append_v2";

        try {
          headers["Dropbox-API-Arg"] = JSON.stringify({
            cursor: { session_id: sessionId, offset: offset },
            close: false,
          });

          offset += chunkSize;

          if (savedOffset <= offset) {
            let response = await fetchWithRetry(url, {
              method: "POST",
              headers: headers,
              body: body,
            });

            let data = await response?.json();

            if (response?.status === 200) {
              console.log("1.Chunk uploaded successfully: ", data, {
                sessionId,
              });

              handleUpdateNotification();
            } else {
              console.log("1.Error in chunk upload: ", data, { offset });

              eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
                data,
                userName,
                companyPrefix,
              });

              await BackgroundJob.stop();

              Alert.alert(
                "Something went wrong",
                "Failed to continue upload, please try again and if the problem persists, please upload from the start",
                [{ text: "OK", onPress: () => console.log("OK Pressed") }],
                { cancelable: false }
              );

              setCurrentChunk(0);

              return -1;
            }
          }
        } catch (error) {
          console.error("Failed to upload the chunk: ", error);
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to upload the chunk, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
            error: error,
            offset: offset,
            userName,
            companyPrefix,
          });

          return -1;
        }
      } else {
        url += "finish";
        headers["Dropbox-API-Arg"] = JSON.stringify({
          cursor: { session_id: sessionId, offset: offset },
          commit: {
            path: `/${cloudStorageFolder}/${destinationPath}.zip`,
            mode: "add",
            autorename: true,
            mute: false,
          },
        });

        let response;
        try {
          response = await fetchWithRetry(url, {
            method: "POST",
            headers: headers,
            body: body,
          });
        } catch (error) {
          console.error("Failed to finish chunked upload: ", error);
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to finish the upload, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
            error: error,
            offset: offset,
            userName,
            companyPrefix,
          });

          return -1;
        }

        let data = await response.json();

        if (response?.status === 200) {
          console.log("2.Chunk finish successfully: ", data);

          handleUpdateNotification();
        } else {
          console.log("2.Error in chunk upload: ", data, { offset });
          setCurrentChunk(0);
          return -1;
        }

        setCurrentChunk(0);
        setIsUploading(false);
        return 1;
      }

      start = end;
      dispatch(setOffset({ id: inspectionId, offset: offset }));
    }
  };

  return { calculateTotalChunks, chunkedUpload };
};

export default useChunkedUpload;
