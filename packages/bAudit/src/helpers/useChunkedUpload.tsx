import { <PERSON><PERSON>, Platform } from "react-native";
import { <PERSON><PERSON><PERSON> } from "buffer";
import { useDispatch } from "react-redux";
import { TRACKING_EVENTS, eventsService } from "bcomponents";
import { setOffset, setSessionId } from "../../../core/reducers";

import RNFS from "react-native-fs";
import RNFetchBlob from "react-native-blob-util";
import BackgroundJob from "react-native-background-actions";
import fetchWithRetry from "./fetchWithDelay";

const useChunkedUpload = () => {
  const dispatch = useDispatch();

  const calculateTotalChunks = async (filePath) => {
    const fileUri = `${filePath}`;
    const fileInfo = await RNFetchBlob.fs.stat(fileUri);
    const fileSize = fileInfo.size;
    const chunkSize = 4 * 1024 * 1024;
    const result = Math.ceil(fileSize / chunkSize); // Use Math.ceil to round up
    return result;
  };

  /**
   * COMPREHENSIVE DEBUGGING VERSION
   * This function includes extensive logging to debug iOS chunked upload issues.
   *
   * Logging includes:
   * 🚀 Function entry with all parameters
   * 📊 File size calculation and chunk count determination
   * 🔄 Each iteration of the while loop (which chunk is being processed)
   * 📤📥 Before and after each network request (start, append_v2, finish)
   * ✅❌ Response status and data for each API call
   * 📍 Offset and session ID updates
   * 🔧 Background job status checks (especially for iOS)
   * 🎯 Progress through different upload phases (start → append → finish)
   * 🔄 Loop progression and final cleanup
   */
  const chunkedUpload = async (
    filePath: string,
    destinationPath: string,
    cloudStorageFolder: string,
    cloudStorageToken: string,
    inspectionId: string,
    savedOffset = 0,
    savedSessionId: string | null,
    setCurrentChunk: (chunk: number) => void,
    setIsUploading: (isUploading: boolean) => void,
    handleUpdateNotification: () => void,
    userName: string,
    companyPrefix: string
  ) => {
    console.log("🚀 CHUNKED UPLOAD STARTED");
    console.log("📁 File Path:", filePath);
    console.log("📂 Destination Path:", destinationPath);
    console.log("☁️ Cloud Storage Folder:", cloudStorageFolder);
    console.log("🔑 Cloud Storage Token:", cloudStorageToken ? "Present" : "Missing");
    console.log("🆔 Inspection ID:", inspectionId);
    console.log("📍 Saved Offset:", savedOffset);
    console.log("🎫 Saved Session ID:", savedSessionId || "None");
    console.log("👤 User Name:", userName);
    console.log("🏢 Company Prefix:", companyPrefix);
    console.log("📱 Platform:", Platform.OS);

    const chunkSize = 4 * 1024 * 1024;
    const fileUri = `${filePath}`;

    console.log("📊 Getting file stats for:", fileUri);
    const file = await RNFetchBlob.fs.stat(fileUri);
    console.log("📊 File stats retrieved:", JSON.stringify(file, null, 2));

    const fileSize = file.size;
    let start = savedOffset ? savedOffset : 0;
    let sessionId = savedSessionId ? savedSessionId : "";
    let chunkIndex = savedOffset ? savedOffset / chunkSize : 0;
    let offset = savedOffset ? savedOffset : 0;

    console.log("📏 File Size:", fileSize, "bytes");
    console.log("🔢 Chunk Size:", chunkSize, "bytes");
    console.log("🎯 Starting Position:", start);
    console.log("🎫 Initial Session ID:", sessionId || "None");
    console.log("📊 Initial Chunk Index:", chunkIndex);
    console.log("📍 Initial Offset:", offset);

    const totalChunks = Math.ceil(fileSize / chunkSize);
    console.log("📈 Total Chunks Expected:", totalChunks);

    console.log("🔄 Starting upload loop...");

    while (start < fileSize) {
      console.log(`\n🔄 === CHUNK LOOP ITERATION ===`);
      console.log(`📊 Current start position: ${start}`);
      console.log(`📏 File size: ${fileSize}`);
      console.log(`✅ Loop condition (start < fileSize): ${start < fileSize}`);

      // Check if background job is still running (iOS specific check)
      const backgroundJobRunning = BackgroundJob.isRunning();
      console.log(`🔧 Background job running: ${backgroundJobRunning}`);

      if (Platform.OS === "ios" && !backgroundJobRunning) {
        console.log("❌ iOS: Background job stopped, aborting upload");
        setCurrentChunk(0);
        return -1;
      }

      setIsUploading(true);
      chunkIndex++;
      setCurrentChunk(chunkIndex);

      console.log(`📈 Processing chunk ${chunkIndex} of ${totalChunks}`);
      console.log(`📍 Current offset: ${offset}`);
      console.log(`🎫 Current session ID: ${sessionId || "None"}`);

      const end = Math.min(start + chunkSize, fileSize);
      console.log(`📐 Chunk boundaries: start=${start}, end=${end}, size=${end - start}`);

      console.log(`📖 Reading chunk data from file...`);
      const chunk = await RNFS.read(fileUri, chunkSize, start, "base64");
      console.log(`📖 Chunk data read successfully, length: ${chunk.length} characters`);

      let url = "https://content.dropboxapi.com/2/files/upload_session/";
      let headers = {
        Authorization: `Bearer ${cloudStorageToken}`,
        "Content-Type": "application/octet-stream",
        "Dropbox-API-Arg": JSON.stringify({
          close: false,
        }),
      };

      console.log(`🌐 Base URL: ${url}`);
      console.log(`🔑 Headers:`, JSON.stringify(headers, null, 2));

      // Convert base64 string back to binary
      console.log(`🔄 Converting base64 to binary data...`);
      const binaryData = Buffer.from(chunk, "base64");
      let body = binaryData;
      console.log(`📦 Binary data size: ${binaryData.length} bytes`);

      if (start === 0) {
        console.log(`\n🚀 === START PHASE (First Chunk) ===`);
        url += "start";
        console.log(`🌐 Start URL: ${url}`);

        let response;
        try {
          console.log(`📤 Making START request...`);
          console.log(`📤 Request details:`, {
            method: "POST",
            url: url,
            bodySize: body.length,
            headers: headers
          });

          response = await fetchWithRetry(url, {
            method: "POST",
            headers: headers,
            body: body,
          });

          console.log(`📥 START response received:`, {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok
          });

        } catch (error) {
          console.error("❌ START request failed:", error);
          console.error("❌ Error details:", JSON.stringify(error, null, 2));
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to upload the chunk, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(
            TRACKING_EVENTS.CHUNK_UPLOAD_START_FAILED,
            {
              error: error,
              offset: offset,
              userName,
              companyPrefix,
            }
          );

          return -1;
        }

        console.log(`📥 Parsing START response JSON...`);
        let data;
        try {
          data = await response.json();
          console.log(`📥 START response data:`, JSON.stringify(data, null, 2));
        } catch (jsonError) {
          console.error("❌ Failed to parse START response JSON:", jsonError);
          await BackgroundJob.stop();
          return -1;
        }

        if (response === -1) {
          console.log("❌ START call returned -1:", data);
          await BackgroundJob.stop();
          return -1;
        }

        if (response?.status !== 200) {
          console.log(`❌ START call failed with status ${response?.status}:`, data);
          console.log(`❌ Response headers:`, response?.headers);
          await BackgroundJob.stop();
          return -1;
        }

        console.log(`✅ START call successful!`);
        console.log(`📤 Updating notification...`);
        handleUpdateNotification();

        const previousOffset = offset;
        offset += chunkSize;
        sessionId = data.session_id;

        console.log(`📍 Offset updated: ${previousOffset} → ${offset}`);
        console.log(`🎫 Session ID received: ${sessionId}`);
        console.log(`💾 Dispatching setSessionId to Redux...`);

        dispatch(setSessionId({ id: inspectionId, sessionId: sessionId }));
      } else if (end < fileSize) {
        console.log(`\n📎 === APPEND PHASE (Middle Chunk) ===`);
        url += "append_v2";
        console.log(`🌐 Append URL: ${url}`);

        try {
          const dropboxApiArg = {
            cursor: { session_id: sessionId, offset: offset },
            close: false,
          };

          headers["Dropbox-API-Arg"] = JSON.stringify(dropboxApiArg);
          console.log(`🔑 Updated headers with cursor:`, JSON.stringify(headers, null, 2));
          console.log(`📍 Using offset for cursor: ${offset}`);
          console.log(`🎫 Using session ID: ${sessionId}`);

          const previousOffset = offset;
          offset += chunkSize;
          console.log(`📍 Offset updated: ${previousOffset} → ${offset}`);

          console.log(`🔍 Checking if should upload (savedOffset: ${savedOffset}, current offset: ${offset})`);
          if (savedOffset <= offset) {
            console.log(`✅ Proceeding with APPEND request...`);
            console.log(`📤 Request details:`, {
              method: "POST",
              url: url,
              bodySize: body.length,
              headers: headers
            });

            let response = await fetchWithRetry(url, {
              method: "POST",
              headers: headers,
              body: body,
            });

            console.log(`📥 APPEND response received:`, {
              status: response?.status,
              statusText: response?.statusText,
              ok: response?.ok
            });

            console.log(`📥 Parsing APPEND response JSON...`);
            let data;
            try {
              data = await response?.json();
              console.log(`📥 APPEND response data:`, JSON.stringify(data, null, 2));
            } catch (jsonError) {
              console.error("❌ Failed to parse APPEND response JSON:", jsonError);
            }

            if (response?.status === 200) {
              console.log("✅ APPEND chunk uploaded successfully!");
              console.log("📊 Success data:", data);
              console.log("🎫 Session ID:", sessionId);

              console.log(`📤 Updating notification...`);
              handleUpdateNotification();
            } else {
              console.log(`❌ APPEND chunk upload failed with status ${response?.status}`);
              console.log("❌ Error data:", data);
              console.log("📍 Current offset:", offset);

              eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
                data,
                userName,
                companyPrefix,
              });

              await BackgroundJob.stop();

              Alert.alert(
                "Something went wrong",
                "Failed to continue upload, please try again and if the problem persists, please upload from the start",
                [{ text: "OK", onPress: () => console.log("OK Pressed") }],
                { cancelable: false }
              );

              setCurrentChunk(0);

              return -1;
            }
          } else {
            console.log(`⏭️ Skipping APPEND request (savedOffset ${savedOffset} > current offset ${offset})`);
          }
        } catch (error) {
          console.error("❌ APPEND request failed:", error);
          console.error("❌ Error details:", JSON.stringify(error, null, 2));
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to upload the chunk, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
            error: error,
            offset: offset,
            userName,
            companyPrefix,
          });

          return -1;
        }
      } else {
        console.log(`\n🏁 === FINISH PHASE (Final Chunk) ===`);
        url += "finish";
        console.log(`🌐 Finish URL: ${url}`);

        const dropboxApiArg = {
          cursor: { session_id: sessionId, offset: offset },
          commit: {
            path: `/${cloudStorageFolder}/${destinationPath}.zip`,
            mode: "add",
            autorename: true,
            mute: false,
          },
        };

        headers["Dropbox-API-Arg"] = JSON.stringify(dropboxApiArg);
        console.log(`🔑 FINISH headers:`, JSON.stringify(headers, null, 2));
        console.log(`📍 Using final offset: ${offset}`);
        console.log(`🎫 Using session ID: ${sessionId}`);
        console.log(`📁 Final file path: /${cloudStorageFolder}/${destinationPath}.zip`);

        let response;
        try {
          console.log(`📤 Making FINISH request...`);
          console.log(`📤 Request details:`, {
            method: "POST",
            url: url,
            bodySize: body.length,
            headers: headers
          });

          response = await fetchWithRetry(url, {
            method: "POST",
            headers: headers,
            body: body,
          });

          console.log(`📥 FINISH response received:`, {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok
          });

        } catch (error) {
          console.error("❌ FINISH request failed:", error);
          console.error("❌ Error details:", JSON.stringify(error, null, 2));
          setCurrentChunk(0);
          await BackgroundJob.stop();

          Alert.alert(
            "Error",
            "Failed to finish the upload, please try again later",
            [{ text: "OK", onPress: () => console.log("OK Pressed") }],
            { cancelable: false }
          );

          eventsService.registerEvent(TRACKING_EVENTS.CHUNK_UPLOAD_FAILED, {
            error: error,
            offset: offset,
            userName,
            companyPrefix,
          });

          return -1;
        }

        console.log(`📥 Parsing FINISH response JSON...`);
        let data;
        try {
          data = await response.json();
          console.log(`📥 FINISH response data:`, JSON.stringify(data, null, 2));
        } catch (jsonError) {
          console.error("❌ Failed to parse FINISH response JSON:", jsonError);
          setCurrentChunk(0);
          return -1;
        }

        if (response?.status === 200) {
          console.log("✅ FINISH call successful!");
          console.log("🎉 Upload completed successfully!");
          console.log("📊 Final response data:", data);

          console.log(`📤 Updating final notification...`);
          handleUpdateNotification();
        } else {
          console.log(`❌ FINISH call failed with status ${response?.status}`);
          console.log("❌ Error data:", data);
          console.log("📍 Final offset:", offset);
          setCurrentChunk(0);
          return -1;
        }

        console.log(`🔄 Resetting chunk counter to 0`);
        setCurrentChunk(0);
        console.log(`🔄 Setting isUploading to false`);
        setIsUploading(false);
        console.log(`🎉 Returning success (1)`);
        return 1;
      }

      const previousStart = start;
      start = end;
      console.log(`🔄 Loop iteration complete. Moving to next chunk:`);
      console.log(`📍 Start position updated: ${previousStart} → ${start}`);
      console.log(`📍 Current offset: ${offset}`);
      console.log(`💾 Dispatching setOffset to Redux...`);

      dispatch(setOffset({ id: inspectionId, offset: offset }));

      console.log(`🔄 End of chunk ${chunkIndex}. Continuing to next iteration...`);
      console.log(`📊 Progress: ${start}/${fileSize} bytes (${((start/fileSize)*100).toFixed(1)}%)`);
    }

    console.log(`🏁 Upload loop completed!`);
    console.log(`📊 Final stats: processed ${start} of ${fileSize} bytes`);
  };

  return { calculateTotalChunks, chunkedUpload };
};

export default useChunkedUpload;
