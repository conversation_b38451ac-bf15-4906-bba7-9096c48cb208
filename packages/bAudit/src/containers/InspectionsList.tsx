import React, { useState, useEffect } from "react";
import { <PERSON>, BackHandler, Alert, Platform } from "react-native";
import { useRoute, useIsFocused } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { useMutation } from "react-query";
import RNFetchBlob from "react-native-blob-util";
import RNFS from "react-native-fs";
import BackgroundJob from "react-native-background-actions";

// Helpers
import { saveXMLToZip } from "../helpers/saveXMLToZip";
import { singleUpload } from "../helpers/singleUpload";
import { postIncomingInspection } from "../mutations/postIncomingInspection";
import { putIncomingInspection } from "../mutations/putUploadFinished";
import useChunkedUpload from "../helpers/useChunkedUpload";
import prepareInspectionToSend from "../helpers/prepareInspectionToSend";

// Constants
import RouteNames from "../navigation/RouteNames";

// Components
import UploadInfo from "../components/inspections/uploadInfo";
import InspectionsListTabs from "./InspectionsListTabs";
import ResumeClearUploadButtons from "../components/inspections/resumeCleanUploadButoons";
import SubjectNotesModal from "../components/inspections/subjectNotesModal";

// Redux
import {
  finishSession,
  startSession,
  setFilePaths,
  setUploadStatus,
} from "../../../core/reducers/index";
import { setSelectedForUpload } from "../slices/generalSlice";

const InspectionsList = ({ navigation }) => {
  const dispatch = useDispatch();
  const route = useRoute();
  const isFocused = useIsFocused();

  const { calculateTotalChunks, chunkedUpload } = useChunkedUpload();

  const recycleBin = route.params?.recycleBin;
  const editMode = route.params?.editMode;
  const inspectionId = route.params?.inspectionId;
  const categoryId = route.params?.categoryId;
  const questionId = route.params?.questionId;
  const activeTab = route.params?.title;

  const store = useSelector((state) => state.persist.questionReducer);
  const {
    inspections,
    attachments,
    questionAnswers,
    uploadProgress,
    progress,
    networkPreference,
    cloudStorageToken,
    cloudStorageFolder,
    esbCompanyGuid,
    token,
  } = useSelector((state) =>
    recycleBin ? state.persist.recycleReducer : state.persist.questionReducer
  );
  const {
    selectedForUpload,
    searchTerm,
    isConnectedToNetwork,
    currentNetwork,
    userName,
    companyPrefix,
  } = useSelector((state) => state.root.generalSlice);
  const { baseUrl } = useSelector((state) => state.root.authReducer);
  const [isExporting, setIsExporting] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [currentChunk, setCurrentChunk] = useState(0);
  const [totalChunks, setTotalChunks] = useState(0);
  const [resumeUploadsBool, setResumeUploadsBool] = useState(false);
  const [subject, setSubject] = useState<string | null>(null);
  const [notes, setNotes] = useState<string | null>(null);
  const [open, setOpen] = useState(false);

  const handleNotes = (id, notes) => {
    setNotes((prev) => {
      return { ...prev, [id]: { notes: notes } };
    });
  };

  useEffect(() => {
    navigation.setOptions({
      handleSaveToZip,
      handleSaveAndUploadZip: () => setOpen(true),
    });
  }, [selectedForUpload]);

  useEffect(() => {
    navigation.setOptions({
      isExporting,
      isUploading,
    });
  }, [navigation, route, isExporting, isUploading, isFocused]);

  useEffect(() => {
    recycleBin && dispatch(setSelectedForUpload([]));
  }, [recycleBin]);

  useEffect(() => {
    recycleBin && dispatch(setSelectedForUpload([]));
  }, [recycleBin]);

  useEffect(() => {
    if (BackgroundJob.isRunning()) {
      setIsUploading(true);
    } else {
      setIsUploading(false);
      setTotalChunks(0);
    }
  }, [BackgroundJob, BackgroundJob.isRunning()]);

  useEffect(() => {
    if (!isExporting && !isUploading) {
      inspections.allIds.map(
        (id) =>
          inspections[id].status === "Uploading" &&
          dispatch(setUploadStatus({ id, status: "In Progress" }))
      );
    }
  }, []);

  useEffect(() => {
    const backAction = () => {
      if (selectedForUpload.length > 0) {
        dispatch(setSelectedForUpload([]));

        return true;
      }

      return false;
    };

    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      backAction
    );

    return () => backHandler.remove();
  }, [selectedForUpload]);

  useEffect(() => {
    if (questionId && progress[inspectionId]?.shouldNavigate) {
      navigation.navigate(RouteNames.GeneralStack, {
        screen: RouteNames.Chapters,
        params: {
          editMode: editMode,
          templateID: inspections[inspectionId]?.template,
          categoryId: categoryId,
          questionId: questionId,
          inspectionId: inspectionId,
        },
      });
    }
  }, [route]);

  const resumeUploads = async () => {
    console.log("RESUMING UPLOADS");

    dispatch(setSelectedForUpload([]));

    uploadProgress.allIds.map((id) => handleSelectForUpload(id));
    setResumeUploadsBool(true);
  };

  useEffect(() => {
    if (resumeUploadsBool) {
      handleSaveAndUploadZip();
      setResumeUploadsBool(false);
    }
  }, [resumeUploadsBool]);

  const cleanUploads = () => {
    setIsUploading(false);
    dispatch(finishSession());
  };

  const sleep = (time: any) =>
    new Promise<void>((resolve) => setTimeout(() => resolve(), time));

  const handleUpdateNotification = async () => {
    BackgroundJob.isRunning() &&
      (await BackgroundJob.updateNotification({
        taskDesc: `Uploading to dropbox: ${currentChunk} of ${totalChunks}`,
      }));
  };

  const uploadFileToDropbox = async (
    filePath,
    destinationPath,
    cloudStorageFolder,
    cloudStorageToken,
    inspectionId,
    savedOffset,
    savedSessionId
  ) => {
    const fileUri = `${filePath}`;
    const fileInfo = await RNFetchBlob.fs.stat(fileUri);
    const fileSize = fileInfo.size;

    let result = 0;

    await sleep(1000);

    if (fileSize <= 4 * 1024 * 1024) {
      result = await singleUpload(
        filePath,
        destinationPath,
        cloudStorageFolder,
        cloudStorageToken,
        userName,
        companyPrefix
      );
    } else {
      result = await chunkedUpload(
        filePath,
        destinationPath,
        cloudStorageFolder,
        cloudStorageToken,
        inspectionId,
        savedOffset,
        savedSessionId,
        setCurrentChunk,
        setIsUploading,
        handleUpdateNotification,
        userName,
        companyPrefix
      );
    }

    return result;
  };

  const onListItemPress = (templateID, inspectionId) => {
    navigation.navigate(RouteNames.GeneralStack, {
      screen: RouteNames.Chapters,
      params: {
        templateID: templateID,
        inspectionId: inspectionId,
        editMode: true,
      },
    });
  };

  const handleSelectForUpload = (id) => {
    selectedForUpload.find((item) => item === id) === undefined &&
      dispatch(setSelectedForUpload([...selectedForUpload, id]));

    selectedForUpload.find((item) => item === id) !== undefined &&
      dispatch(
        setSelectedForUpload(selectedForUpload.filter((item) => item !== id))
      );
  };

  const gatherInspections = () => {
    return selectedForUpload.map((id) => {
      const attachments = inspections[id].questionAnswers?.reduce(
        (acc, curr) => {
          const currAttachments = questionAnswers[curr]?.attachments;
          if (currAttachments) {
            acc.push(...currAttachments);
          }
          return acc;
        },
        []
      );

      return {
        inspectionId: id,
        inspectionResult: prepareInspectionToSend(id, store),
        attachments: attachments || 0,
      };
    });
  };

  const handleSaveToZip = async () => {
    const inspectionsToSave = gatherInspections();
    setIsExporting(true);
    const path = `${
      Platform.OS === "ios"
        ? RNFS.DocumentDirectoryPath
        : RNFS.DownloadDirectoryPath
    }/bAudit`;

    // create folder
    if (!(await RNFS.exists(path))) {
      await RNFS.mkdir(path);
      Platform.OS === "android" && (await RNFS.scanFile(path));
    }

    for (const inspection of inspectionsToSave) {
      const { inspectionResult, inspectionId } = inspection;

      dispatch(
        setUploadStatus({ id: inspection.inspectionId, status: "Uploading" })
      );

      const zipFilePath = `${
        inspections[inspection.inspectionId]?.dirPath
      }.zip`;

      if (await RNFS.exists(zipFilePath)) {
        await RNFS.unlink(zipFilePath);
      }

      if (await RNFS.exists(zipFilePath)) {
        await RNFS.unlink(zipFilePath);
      }

      await saveXMLToZip(
        inspectionResult.xml,
        inspection.attachments,
        attachments,
        zipFilePath,
        inspections[inspectionId]?.name
      );

      dispatch(
        setUploadStatus({ id: inspection.inspectionId, status: "In Progress" })
      );
    }

    setIsExporting(false);
  };

  const prepareUpload = useMutation({
    mutationFn: async (file: {
      name: string;
      size: number;
      subject: string;
      notes: string;
      onSuccess: (fileName: string) => void;
    }) => {
      const data = await postIncomingInspection(
        baseUrl,
        token.access_token,
        esbCompanyGuid,
        file.name,
        file?.size,
        file?.subject,
        file?.notes
      );

      return data;
    },
    onSuccess: (data, variables, context) => {
      variables.onSuccess(data.GUID);
    },
    onError: (error, variables, context) => {
      console.log("Error in prepareUpload: ", error);

      Alert.alert(
        "Error",
        "Failed to upload the inspection, please check your internet connection and try again later",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
      setIsUploading(false);
    },
  });

  const handleSaveAndUploadZip = async () => {
    console.log("handleSaveAndUploadZip");

    if (!isConnectedToNetwork) {
      Alert.alert(
        "No internet connection",
        "Please connect to the internet to upload inspections",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
      return;
    }

    if (networkPreference === "wifi" && currentNetwork !== "wifi") {
      Alert.alert(
        "No wifi connection",
        "Please connect to Wi-Fi to upload inspections\n\nIf you want to upload over mobile data, please change your network preference in Menu > Mobile Data Restrictions",
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
        { cancelable: false }
      );
      return;
    }

    setIsExporting(true);

    const inspectionsToSave = gatherInspections();

    if (uploadProgress.allIds.length === 0) {
      dispatch(
        startSession(inspectionsToSave.map((item) => item.inspectionId))
      );
    }

    for (const inspection of inspectionsToSave) {
      const { inspectionResult, inspectionId } = inspection;
      const dir =
        Platform.OS === "ios"
          ? RNFS.DocumentDirectoryPath
          : RNFS.DownloadDirectoryPath;

      const zipFilePath = uploadProgress[inspection.inspectionId]?.filePath
        ? uploadProgress[inspection.inspectionId]?.filePath
        : `${inspections[inspection.inspectionId].dirPath}.zip`;

      // create folder
      if (!(await RNFS.exists(`${dir}/bAudit`))) {
        await RNFS.mkdir(`${dir}/bAudit`);
        Platform.OS === "android" && (await RNFS.scanFile(`${dir}/bAudit`));
      }

      if (
        (await RNFS.exists(zipFilePath)) &&
        !uploadProgress[inspection.inspectionId]?.inspectionId
      ) {
        await RNFS.unlink(zipFilePath);
      }

      !uploadProgress[inspection.inspectionId]?.filePath &&
        (await saveXMLToZip(
          inspectionResult.xml,
          inspection.attachments,
          attachments,
          zipFilePath,
          inspections[inspection.inspectionId]?.name
        ));

      setIsExporting(false);

      setIsUploading(true);

      const backgroundJobConfig = {
        taskName: "Upload to dropbox",
        taskTitle: "Uploading to dropbox",
        taskDesc: "Starting upload to dropbox",
        taskIcon: {
          name: "ic_launcher_round",
          type: "mipmap",
        },
        color: "#FFA200",
        linkingURI: "yourSchemeHere://chat/jane", // See Deep Linking for more info
        parameters: {
          delay: 1000,
        },
        // iOS-specific configuration for better background handling
        ...(Platform.OS === "ios" && {
          progressBar: {
            max: totalChunks,
            value: 0,
            indeterminate: false,
          },
        }),
      };

      const zipStats = await RNFS.stat(zipFilePath);

      const dropboxDestinationPath = `${
        inspections[inspection.inspectionId].name
      }.zip`;

      const tempTotalChunks = await calculateTotalChunks(zipFilePath);
      setTotalChunks((prev) => prev + tempTotalChunks);
      setCurrentChunk(0);

      // If we are resuming we do not prepare upload
      if (
        uploadProgress[inspection.inspectionId]?.inspectionId &&
        uploadProgress[inspection.inspectionId]?.esbExchDocsGuid
      ) {
        setIsUploading(true);

        console.log(
          "Starting reupload to dropbox: ",
          uploadProgress[inspection.inspectionId]?.esbExchDocsGuid
        );

        dispatch(
          setUploadStatus({ id: inspection.inspectionId, status: "Uploading" })
        );

        const uploadWrapper = async ({ delay }) => {
          await sleep(delay);

          const result = await uploadFileToDropbox(
            uploadProgress[inspection.inspectionId]?.filePath,
            uploadProgress[inspection.inspectionId]?.esbExchDocsGuid,
            cloudStorageFolder,
            cloudStorageToken,
            inspection.inspectionId,
            uploadProgress[inspection.inspectionId]?.offset,
            uploadProgress[inspection.inspectionId]?.sessionId
          );

          if (result === 1) {
            try {
              // Notify server that upload is complete
              await putIncomingInspection(
                baseUrl,
                token.access_token,
                esbCompanyGuid,
                uploadProgress[inspection.inspectionId]?.esbExchDocsGuid
              );

              dispatch(
                setUploadStatus({
                  id: inspection.inspectionId,
                  status: "Uploaded",
                })
              );

              console.log("Upload to dropbox finished: ", {
                esbExchDocsGuid:
                  uploadProgress[inspection.inspectionId]?.esbExchDocsGuid,
              });

              dispatch(finishSession({ id: inspection.inspectionId }));
            } catch (error) {
              console.error("Failed to notify server of upload completion: ", error);
              dispatch(
                setUploadStatus({ id: inspection.inspectionId, status: "Failed" })
              );
            }
          } else {
            dispatch(
              setUploadStatus({ id: inspection.inspectionId, status: "Failed" })
            );
          }

          dispatch(setSelectedForUpload([]));
          setIsUploading(false);

          return 0;
        };

        return await BackgroundJob.start(uploadWrapper, backgroundJobConfig);
      }

      await prepareUpload.mutate({
        name: dropboxDestinationPath,
        size: zipStats.size,
        subject: `${inspections[inspection.inspectionId].name}.zip`,
        notes: notes?.[inspection.inspectionId]?.notes || "",
        onSuccess: async (filename) => {
          setIsUploading(true);

          console.log("Starting upload to dropbox: ", filename);

          dispatch(
            setUploadStatus({
              id: inspection.inspectionId,
              status: "Uploading",
            })
          );

          dispatch(
            setFilePaths({
              id: inspection.inspectionId,
              filePath: zipFilePath,
              destinatinationFilePath: filename,
            })
          );

          const uploadWrapper = async ({ delay }) => {
            await sleep(delay);

            const result = await uploadFileToDropbox(
              zipFilePath,
              filename,
              cloudStorageFolder,
              cloudStorageToken,
              inspection.inspectionId,
              0, // savedOffset for new uploads
              null // savedSessionId for new uploads
            );

            console.log("Upload to dropbox finished: ", { filename });

            if (result === 1) {
              try {
                // Notify server that upload is complete
                await putIncomingInspection(
                  baseUrl,
                  token.access_token,
                  esbCompanyGuid,
                  filename
                );

                dispatch(
                  setUploadStatus({
                    id: inspection.inspectionId,
                    status: "Uploaded",
                  })
                );

                dispatch(finishSession({ id: inspection.inspectionId }));
              } catch (error) {
                console.error("Failed to notify server of upload completion: ", error);
                dispatch(
                  setUploadStatus({
                    id: inspection.inspectionId,
                    status: "Failed",
                  })
                );
              }
            } else {
              dispatch(
                setUploadStatus({
                  id: inspection.inspectionId,
                  status: "Failed",
                })
              );
            }

            setIsUploading(false);
          };

          await BackgroundJob.start(uploadWrapper, backgroundJobConfig);

          dispatch(setSelectedForUpload([]));
        },
      });
    }
  };

  const onCancelUploadPress = async () => {
    await BackgroundJob.stop();
    inspections.allIds.map(
      (id) =>
        inspections[id].status === "Uploading" &&
        dispatch(setUploadStatus({ id, status: "In Progress" }))
    );
    setIsUploading(false);
  };

  return (
    <View style={{ flex: 1 }}>
      <InspectionsListTabs
        navigateActiveTab={activeTab}
        onListItemPress={onListItemPress}
        handleSelectForUpload={handleSelectForUpload}
        recycleBin={recycleBin}
      />

      {isUploading && (
        <UploadInfo
          currentChunk={currentChunk}
          totalChunks={totalChunks}
          onCancelPress={onCancelUploadPress}
        />
      )}

      {!recycleBin &&
        uploadProgress?.allIds?.length > 0 &&
        !isUploading &&
        !isExporting && (
          <ResumeClearUploadButtons
            resumeUploads={resumeUploads}
            cleanUploads={cleanUploads}
          />
        )}

      {/* {!recycleBin && !isUploading && selectedForUpload.length > 0 && (
        <InspectionActionButtons
          isExporting={isExporting}
          handleSaveToZip={handleSaveToZip}
          openModal={setOpen}
        />
      )} */}

      <SubjectNotesModal
        open={open}
        setOpen={setOpen}
        subjects={selectedForUpload.map((id) => {
          return { id: id, name: inspections[id]?.name + ".zip" };
        })}
        setSubject={setSubject}
        notes={notes}
        setNotes={handleNotes}
        isExporting={isExporting}
        handleSaveAndUploadZip={handleSaveAndUploadZip}
      />
    </View>
  );
};

export default InspectionsList;
