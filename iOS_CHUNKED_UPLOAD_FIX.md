# iOS Chunked Upload Fix - bAudit Mobile App

## Problem Summary
The chunked file upload in the bAudit mobile app was getting stuck on "uploading chunk 1 of 3" specifically on iOS devices, while Android worked correctly. The issue was related to several iOS-specific problems and missing server notifications.

## Root Causes Identified

### 1. **Missing Server Notification**
- After successful Dropbox upload, the app was not calling `putIncomingInspection` to notify the server that the upload was complete
- This caused the server to never know the upload finished, leading to stuck uploads

### 2. **Incorrect Offset Calculation**
- In the finish call of the chunked upload, the code was using `start` instead of `offset` for the cursor position
- This could cause Dropbox to reject the finish request

### 3. **iOS Background Task Limitations**
- iOS has strict background processing limitations that can suspend network requests
- The app wasn't properly handling iOS background task lifecycle

### 4. **Insufficient Error Handling**
- The finish call in chunked upload wasn't using `fetchWithRetry` for better error handling
- Missing proper error handling for iOS-specific network issues

## Fixes Applied

### 1. **Fixed Offset Calculation**
**File:** `packages/bAudit/src/helpers/useChunkedUpload.tsx`
- Changed `cursor: { session_id: sessionId, offset: start }` to `cursor: { session_id: sessionId, offset: offset }`
- Added proper error handling with `fetchWithRetry` for the finish call

### 2. **Added Server Notification**
**File:** `packages/bAudit/src/containers/InspectionsList.tsx`
- Added import for `putIncomingInspection`
- Added server notification calls after successful Dropbox uploads
- Added proper error handling for server notification failures

### 3. **Enhanced iOS Background Task Handling**
- Added iOS-specific background job checks in the upload loop
- Enhanced background job configuration with progress bar for iOS
- Added background job running status checks

### 4. **Improved Error Handling**
- Added comprehensive error handling for all network requests
- Added proper cleanup when uploads fail
- Added iOS-specific error tracking

## Code Changes Made

### useChunkedUpload.tsx Changes:
```typescript
// Fixed offset calculation in finish call
cursor: { session_id: sessionId, offset: offset }

// Added iOS background job check
if (Platform.OS === "ios" && !BackgroundJob.isRunning()) {
  console.log("Background job stopped, aborting upload");
  setCurrentChunk(0);
  return -1;
}

// Added fetchWithRetry for finish call
response = await fetchWithRetry(url, {
  method: "POST",
  headers: headers,
  body: body,
});
```

### InspectionsList.tsx Changes:
```typescript
// Added server notification after successful upload
if (result === 1) {
  try {
    await putIncomingInspection(
      baseUrl,
      token.access_token,
      esbCompanyGuid,
      filename
    );
    // Success handling...
  } catch (error) {
    // Error handling...
  }
}
```

## Testing Recommendations

### 1. **Test Upload Flow**
- Test with files larger than 4MB to trigger chunked upload
- Test on iOS devices with different iOS versions
- Test with app backgrounding during upload

### 2. **Test Error Scenarios**
- Test with poor network conditions
- Test with app being killed during upload
- Test resume functionality

### 3. **Monitor Logs**
- Check Reactotron logs for proper chunk progression
- Verify server notification calls are made
- Monitor background job status

## Additional Recommendations

### 1. **Background Task Improvements**
Consider implementing iOS Background App Refresh handling:
```typescript
// Add to Info.plist
<key>UIBackgroundModes</key>
<array>
  <string>background-processing</string>
  <string>background-fetch</string>
</array>
```

### 2. **Network Monitoring**
Add network state monitoring to pause/resume uploads based on connectivity.

### 3. **Progress Persistence**
Consider persisting upload progress to AsyncStorage for better resume capability.

### 4. **Timeout Handling**
Add configurable timeouts for different network conditions.

## Expected Behavior After Fix

1. **Chunk Progression**: Upload should progress from chunk 1 → 2 → 3 → completion
2. **Server Notification**: Server should receive notification when upload completes
3. **iOS Background**: Upload should continue properly when app is backgrounded
4. **Error Recovery**: Better error messages and recovery options
5. **Resume Capability**: Improved ability to resume failed uploads

## Monitoring Points

- Watch for "Chunk uploaded successfully" logs progressing through all chunks
- Verify "Upload to dropbox finished" appears after all chunks
- Check that server receives the completion notification
- Monitor background job status throughout the upload process
